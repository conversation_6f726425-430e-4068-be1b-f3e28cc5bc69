import time
import traceback
import pandas as pd
from langchain.schema import StrOutputParser
import requests
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
import os
from loguru import logger
from retrying import retry

# 配置常量
INPUT_FILE_PATH = r'/教学案例.xlsx'
OUTPUT_FILE_PATH = '教学案例分析结果.csv'
OPENAI_API_KEY = "sk-wfthjtvqdahnzaelbefbrvbahsbpicgeopwascxnwguvvwtd"
SILICONFLOW_API_BASE = "https://api.siliconflow.cn/v1"
# SILICONFLOW_MODEL = 'deepseek-chat'
SILICONFLOW_MODEL = 'deepseek-ai/DeepSeek-V3'
# SILICONFLOW_MODEL = 'Qwen/Qwen3-235B-A22B'
# SILICONFLOW_MODEL = 'qwen-plus-latest'
KNOWLEDGE_API_URL = 'http://47.99.155.171:6000/recall_filter'

# CSV输出列名
INPUT_COLUMNS = ['主诉', '刻下症状', '体征', '基础病机', '六经证候', '四证', '处方']
OUTPUT_COLUMNS = ['唯一ID号', '病情信息', '已做的诊断', '证候召回数据', '证候分析', '药方召回数据', '药方分析', '处理时间']


class TCMAnalyzer:
    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=OPENAI_API_KEY,
            base_url=SILICONFLOW_API_BASE,
            model=SILICONFLOW_MODEL
        )

    @retry(stop_max_attempt_number=3)
    def ask_llm(self, prompt_template, input_data: dict):
        try:
            st = time.time()
            prompt = ChatPromptTemplate.from_template(prompt_template)
            output_parser = StrOutputParser()
            chain = prompt | self.llm | output_parser
            chain = chain.bind(response_format={"type": "json_object"})
            resp = chain.invoke(input_data)
            return resp
        except Exception as e:
            logger.error(f"调用大模型失败： {traceback.format_exc()}")
            return ""
        finally:
            logger.debug(f"LLM回答耗时：{round(time.time() - st, 4)}")

    def read_excel(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件并验证必要列"""
        try:
            df = pd.read_excel(file_path)
            missing_cols = [col for col in INPUT_COLUMNS if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Excel文件缺少必要列: {missing_cols}")

            logger.info(f"成功读取Excel文件，共{len(df)}行数据")
            return df

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise

    def search_knowledge(self, knowledge_query: str, books: list) -> dict:
        """调用知识库API进行知识检索"""
        logger.debug("开始检索。。。")
        payload = {
            "books": books,
            "spo_model": 'qwen-turbo',
            "filter_model": 'qwen-turbo',
            "content_num": 10,
            "spo_num": 2,
            "query": knowledge_query
        }

        try:
            start_time = time.time()
            response = requests.post(KNOWLEDGE_API_URL, json=payload, timeout=30)
            response.raise_for_status()
            elapsed_time = time.time() - start_time

            logger.debug(f"知识检索完成，耗时: {elapsed_time:.2f}秒")
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"知识检索API调用失败: {e}")
            return {}

    def generate_syndrome_analysis(self, base_info: str, syndrome: str):
        """生成证候分析"""
        logger.debug(f"开始分析病机生成...")
        knowledge_query = f'''
{base_info}
-请根据以上病历按照以下步骤进行分析
(1)基础病机分析：根据《经典经方病机方机体系》中的基础病机（表束、表寒、中风、伤营、伤精、太阴伤血、血少、里寒、里虚、淡饮、阳明里热、里燥、里结、水热、外热、外燥、外结），逐一分析“主诉”和“刻下症状”如何分别对应到基础病机，同一个症状可能有不同的病机归类，请把全部的可能列举出来，并根据其他症状推测最有可能的基础病机。
**注意"体征"中，不需要分析舌象、脉象、眼睑颜色、腹部触诊对应的基础病机**
**不要出现上述基础病机范围之外的病机**
(2)六经证候分析：分析患者的基础病机组合起来，为何能得出"六经证候"和“四证”的结果，分析水、火、气、血四证中哪一个或哪两个最突出。
'''
        books = ["基础病机", "经典经方学术体系传习汇编_许家栋", "经典经方病机方机体系",
                 "临床医案解伤寒三期讲课实录_伤寒论讲座_许家栋", "临床医案解伤寒二期讲课实录_伤寒论讲座_许家栋",
                 "临床医案解伤寒五期讲课实录_经典经方第五期课程_伤寒论讲座_许家栋",
                 "临床医案解伤寒四期讲课实录_伤寒论讲座_许家栋"]
        knowledge_recall = self.search_knowledge(knowledge_query, books)
        prompt_template = """
你的名字是轩岐问对，擅长回答与医学相关的问题
# 输入
## 病情信息
{base_info}

## 这是参考的知识库内容：
> {recall}

## 证候
{syndrome}

这些知识库是一个个的片段，可能有助于回答问题，也可能对回答问题没有帮助，可能需要整合后才能回答问题，也可能需要经过一定的推理才能回答问题。
现在要求你：
1.已知病情信息中患者的主诉、症状、体征，从知识库片段中推理出和病情信息中患者的六经证候、四证一致的答案，需要一步步地进行思考、推理；
2.进行结构化回答：

### 第一部分：知识库整合
具体整合内容，越详细越好。阐述的时候必须引用参考知识库，并在句尾标注索引号。如:知识库整合内容[1][4]

### 第二部分：基础病机分析
[病机1]：……（症状及分析）
[病机2]：……（症状及分析）
……

### 第三部分：六经证候分析
整合基础病机，通过分析推理，得出患者六经证候的具体推理内容，越详细越好

### 第四部分：总结
[证候]：……
[四证]：……

3.对于参考价值不大的知识片段，不要纳入思考和推理的范围里
4.回答越详细越好、推理逻辑越严密越好；
5.如果参考知识条文为空列表，则直接回答问题

请现在给出你的回答：
        """
        try:
            syndrome_input_data = {
                "base_info": base_info,
                "syndrome": syndrome,
                "recall": knowledge_recall
            }
            answer = self.ask_llm(prompt_template, syndrome_input_data)
            if answer:
                logger.success("证候分析生成成功")
            else:
                logger.error("证候分析生成失败")
            return knowledge_recall, answer
        except Exception as e:
            logger.error(f"证候分析生成失败: {traceback.format_exc()}")
            return knowledge_recall, f"证候分析生成失败: {str(e)}"

    def generate_prescription_analysis(self, base_info: str, prescription: str, syndrome_analysis: str):
        """生成药方分析"""
        logger.debug(f"开始分析药方生成...")
        knowledge_query = f'''
{base_info}
-请根据以上病历按照以下步骤进行分析
##方阵分析：根据知识库中"方阵"相关的知识片段，寻找最适合治疗患者的六经证候、四证的方阵，提供详细的逐步推理分析过程。
##药物分析：结合患者的病机和症状，全面思考方阵中包含的所有方剂，推理得到1个最适合患者的方剂（要求该方剂所包含的药物性味、功效能针对性地治疗患者的症状、病机），提供详细的逐步推理分析过程。
'''
        books = ["经方本草临床药解","经方类方与方阵衍化","方阵"]
        knowledge_recall = self.search_knowledge(knowledge_query, books)
        prompt_template = '''
你是一位中医经方大家，擅长应用经方治疗临床各科杂病。 
# 输入
{base_info} 
## 中医诊断思路
{syndrome_analysis}
## 实际开方
{prescription}

## 参考的知识库内容：
> {recall} 

# 输出
这些知识库是一个个的片段，可能有助于回答问题，也可能对回答问题没有帮助，可能需要整合后才能回答问题，也可能需要经过一定的推理才能回答问题。现在要求你： 
现在要求你：
1.已知病情信息中患者的主诉、症状、体征、六经证候、四证，从知识库片段中推理出和患者的处方（包括方名和药物组成）一致的答案，需要一步步地进行思考、推理；
2.进行结构化回答：

### 第一部分：知识库整合
具体整合内容，越详细越好。阐述的时候必须引用参考知识库，并在句尾标注索引号。如:知识库整合内容[1][4]

### 第三部分：方阵分析
[方阵]：……
[分析]：……

### 第四部分：本草药势分析
[最适合的方剂]：……
[药物1]：……（性味，功效，对治患者的什么病症）
[药物2]：……
[分析]：……

3.对于参考价值不大的知识片段，不要纳入思考和推理的范围里
4.回答越详细越好、推理逻辑越严密越好；
5.如果参考知识条文为空列表，则直接回答问题

        '''
        try:
            prep_input_data = {
                "base_info": base_info,
                "syndrome_analysis": syndrome_analysis,
                "prescription": prescription,
                "recall": knowledge_recall
            }
            answer = self.ask_llm(prompt_template, prep_input_data)
            logger.debug("药方分析生成成功")
            return knowledge_recall, answer
        except Exception as e:
            logger.error(f"药方分析生成失败: {e}")
            return knowledge_recall, f"药方分析生成失败: {str(e)}"

    def append_result_to_csv(self, result_data: dict) -> bool:
        """追加结果到CSV文件"""
        try:
            # 创建输出目录
            output_dir = os.path.dirname(OUTPUT_FILE_PATH)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logger.info(f"创建输出目录: {output_dir}")

            # 检查CSV文件是否存在
            file_exists = os.path.exists(OUTPUT_FILE_PATH)

            # 创建DataFrame
            df = pd.DataFrame([result_data])

            # 追加写入CSV
            df.to_csv(
                OUTPUT_FILE_PATH,
                mode='a',  # 追加模式（如果文件存在，就追加数据）
                header=not file_exists,  # 如果文件不存在，就写入表头；否则不写
                index=False,  # 不写入行索引（原错误：idx=False → 正确：index=False）
                encoding='utf-8'  # 使用 UTF-8 编码（避免中文乱码）
            )
            logger.debug(f"{result_data['唯一ID号']} 处理完成，结果已保存到CSV文件")
            return True
        except Exception as e:
            logger.error(f"写入CSV文件失败: {traceback.format_exc()}")
            return False

    def process_single_case(self, idx, row: pd.Series) -> bool:
        """处理单个病例"""
        try:
            logger.info(f"开始处理病例 {idx+1}")

            # 格式化患者信息
            base_info = f"""
- 主诉: {row['主诉']}
- 刻下症状: {row['刻下症状']}
- 体征: {row['体征']}
"""
            # syndrome = row['六经证候']
            prescription = row['处方']
            lj_syndrome = row['六经证候']
            st = time.time()
            # 生成分析
            synd_knowledge_recall, syndrome_analysis = self.generate_syndrome_analysis(base_info, lj_syndrome)
            prep_knowledge_recall, prescription_analysis = self.generate_prescription_analysis(base_info, prescription, syndrome_analysis)

            # 准备输出数据
            result_data = {
                '唯一ID号': getattr(row, '唯一ID号', idx),
                '病情信息': base_info,
                '已做的诊断': lj_syndrome,
                '证候召回数据': synd_knowledge_recall,
                '证候分析': syndrome_analysis,
                '药方召回数据': prep_knowledge_recall,
                '药方分析': prescription_analysis,
                '处理时间': round(time.time() - st, 4)
            }

            # 追加到CSV文件
            success = self.append_result_to_csv(result_data)

            if success:
                logger.info(f"病例 {idx} 处理完成")
                return True
            else:
                logger.warning(f"病例 {idx} 结果保存失败")
                return False

        except Exception as e:
            logger.error(f"处理病例 {idx} 时出错: {traceback.format_exc()}")
            return False

    def run(self):
        """主运行流程"""
        try:
            # 检查输入文件
            if not os.path.exists(INPUT_FILE_PATH):
                logger.error(f"输入Excel文件不存在: {INPUT_FILE_PATH}")
                return

            # 读取Excel文件
            df = self.read_excel(INPUT_FILE_PATH)

            total_cases = len(df)
            success_count = 0
            failed_count = 0

            logger.info(f"开始处理 {total_cases} 个病例...")

            # 逐行处理
            for idx, row in df.iterrows():
                if self.process_single_case(idx, row):
                    success_count += 1
                else:
                    failed_count += 1

                # 进度报告
                if (idx + 1) % 10 == 0 or (idx + 1) == total_cases:
                    logger.info(f"处理进度: {idx + 1}/{total_cases}, 成功: {success_count}, 失败: {failed_count}")

            logger.info(f"处理完成! 总计: {total_cases}, 成功: {success_count}, 失败: {failed_count}")

        except Exception as e:
            logger.error(f"主流程执行失败: {e}")


def main():
    """主函数"""
    try:
        logger.info("中医辨证分析系统启动")
        analyzer = TCMAnalyzer()
        analyzer.run()
        logger.info("中医辨证分析系统结束")

    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()
