from openai import OpenAI



client = OpenAI(
    api_key='dummy',
    base_url="http://10.0.11.94:10000/v1",
)

stream = client.chat.completions.create(
    model="DeepSeek-R1",
    messages=[
        {'role': 'user', 'content': '13*5等于多少'}
    ],
    stream=True
)

# 遍历流式响应，使用属性访问
for chunk in stream:
    # 获取 delta 对象
    delta = chunk.choices[0].delta
    # 直接使用属性访问 delta.content
    content = delta.content if hasattr(delta, "content") else ""
    if content:
        print(content, end="", flush=True)
print()