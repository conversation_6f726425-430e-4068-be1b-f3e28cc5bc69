import requests
import json

prompt = """### Task
生成 SQL 查询来回答 [QUESTION]{question}[/QUESTION]

＃＃＃ 指示
- 如果您无法使用可用的数据库架构回答问题，请只返回“我不知道” 不要有其他sql语句


### Database Schema
药方订单表如下:
CREATE TABLE `boc_recipel` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一编号',
  `pay_orderid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '支付单号',
  `pay_orderid_six` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '支付单号后6位',
  `orderid_origin` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '复诊开方,原始订单号',
  `type_id` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '普通方方   type_id=1\n 线下转方  type_id=2\n 膏方  	 type_id=3\n 中成药  	 type_id=18',
  `recipel_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '1为手机开方，2为照片开方，3为微信开方',
  `form_id` int(1) NOT NULL DEFAULT '0' COMMENT '剂型\n101:中药饮片\n102:浓缩丸\n103:颗粒剂\n104:粉剂\n105:大密丸\n130:膏方\n118:中成药',
  `is_decoction` int(1) NOT NULL DEFAULT '0' COMMENT '服用方式\n0:自煎\n1:代煎\n2:浓缩丸 (小水丸,小蜜丸)\n3:颗粒剂\n4:粉剂\n5:大密丸\n6:水丸\n7:水蜜丸\n31:流浸膏小包装\n32:干切片小包装\n33:膏体罐装 \n18:中成药',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `did` int(11) NOT NULL COMMENT '医生ID',
  `sort_id` int(10) NOT NULL DEFAULT '0' COMMENT '排序编号',
  `audit` int(1) NOT NULL DEFAULT '0' COMMENT '0:1审核',
  `flag` int(1) NOT NULL DEFAULT '0' COMMENT '0:1推荐',
  `reg_id` int(10) NOT NULL DEFAULT '0' COMMENT '挂号ID,加号ID',
  `registration_fee` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否收诊金0否1是',
  `did_audit` int(11) NOT NULL DEFAULT '0' COMMENT '审核医生ID',
  `time_create` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `time_pay` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间',
  `time_deadline` int(11) NOT NULL DEFAULT '0' COMMENT '付款截止时间',
  `time_re_day` int(11) NOT NULL DEFAULT '0' COMMENT '诊后回访天数',
  `time_re` int(11) NOT NULL DEFAULT '0' COMMENT '提示回访时间',
  `status_pay` int(11) NOT NULL DEFAULT '0' COMMENT '0未支付,\n1支付成功,\n ',
  `timeline` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL DEFAULT '0' COMMENT '患者id',
  `patient_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '患者姓名(contact)',
  `patient_gender` int(1) NOT NULL DEFAULT '0' COMMENT '患者性别（0女；1男, 3未知）',
  `patient_age` float(16,2) NOT NULL DEFAULT '0.00' COMMENT '患者年龄（以前是收件人年龄）',
  `pack` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '包装方式\r\n\r\n流浸膏小包装\r\n干切片小包装\r\n膏体罐装',
  `pill_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '丸剂类型\r\nWATER,HONEY',
  `time_check` int(11) NOT NULL DEFAULT '0' COMMENT '审方时间',
  `time_nu` int(10) NOT NULL DEFAULT '0' COMMENT '填写快递号时间',
  `time_ship` int(11) NOT NULL DEFAULT '0' COMMENT '发货时间',
  `time_fax` int(11) NOT NULL DEFAULT '0' COMMENT '传真时间',
  `shipping_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递公司名称',
  `take_days` int(11) NOT NULL DEFAULT '0' COMMENT '丸剂服用天数',
  `amount` int(11) NOT NULL DEFAULT '1' COMMENT '药萜数',
  `photo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处方图片',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '虚拟删除，0正常，1删除',
  `is_checked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单审核状态。默认为0，1为审核通过，2为审核不通过',
  `invoice_apply` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0可申请1已经申请2禁止申请',
  `is_pre` tinyint(4) NOT NULL DEFAULT '0' COMMENT '开方类型，0直接开方,需要审核或录入处方成功后, 1  照片开方(未录入成功) 2医助开方 ,70,自助开方商品,71,自助开方用户订单',
  `is_timeout` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否超时签收 (5天)  0否 1是',
  `is_judge` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否评价0未评，1已经评，2禁止评',
  `money_doctor` float(16,2) NOT NULL DEFAULT '0.00' COMMENT '医生诊金',
  `origin` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '来源\r\n图文:inquiry\r\n诊后:followup\r\n抢答:random\r\n其他:空字符串',
  `excel_down` int(1) NOT NULL DEFAULT '0' COMMENT 'excel下载(现针对颗粒剂厂商 后期可通用) 0未下载 1下载',
  `label_print` int(1) NOT NULL DEFAULT '0' COMMENT '打印标记 0未打印 1打印',
  `district` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '药库 地区',
  `supplier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '药厂供应商',
  `nu` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '快递单号',
  `is_ship` int(3) NOT NULL DEFAULT '0' COMMENT 'is_ship状态：\r\n0,默认\r\n2,已发送供应商(含纸质传真)\r\n4,已电子传真.\r\n\r\n1,揽件(已经发货)\r\n5 途中\r\n3,签收\r\n\r\n10,接方\r\n\r\n华东状态:\r\n浸泡\r\n审议\r\n签收\r\n配方\r\n复核\r\n派件\r\n煎药\r\n收件\r\n接方\r\n\r\n \r\n\r\n',
  `tp_orderid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '第三方单号\r\n华东单号:HD开头',
  `remind` int(1) NOT NULL DEFAULT '0' COMMENT '通知用户付款\r\n1,到期前24小时通知\r\n2,到期前1小时通知',
  `current_contact_id` int(10) NOT NULL DEFAULT '0' COMMENT '医助ID manager-id',
  `storage_id` int(10) NOT NULL DEFAULT '0' COMMENT '药库',
  `pre_entry_id` int(11) NOT NULL DEFAULT '0' COMMENT '照片开方id',
  `is_recipel_invest` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启用药反馈单。0为没有，1为开启',
  `recipel_invest_days` int(11) NOT NULL DEFAULT '0' COMMENT '反馈单发送的天数',
  `recipel_invest_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '用药反馈单发送时间，Y-m-d H:i',
  `recipel_invest_detail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用药反馈详情',
  `recipel_invest_detail_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '问诊单是否未读 0：未读 1已读',
  `usage_mode` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '使用方式类型',
  `dose` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '剂量',
  `times_per_day` int(11) NOT NULL COMMENT '次/天(每天服用)',
  `service_fee_rate` decimal(16,14) DEFAULT '0.00000000000000' COMMENT '医生该订单服务费提高比率,null为默认,0为不收',
  `is_after_sales` int(1) NOT NULL DEFAULT '0' COMMENT '是否有售后',
  `tmpno` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '甘草编号 ',
  `recipel_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '药方显示名',
  `kinds` int(11) NOT NULL DEFAULT '0' COMMENT '中药味数',
  `recipe_weight` int(11) NOT NULL COMMENT '中药总处方重量(克)',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `prescript_title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '协定方名称',
  `prescript_id` int(11) NOT NULL DEFAULT '0' COMMENT '协定处方 id',
  `doctor_province` int(10) NOT NULL DEFAULT '0' COMMENT '医生省份',
  `doctor_city` int(10) NOT NULL DEFAULT '0' COMMENT '医生城市',
  `create_dt` date NOT NULL COMMENT '创建日期',
  `is_show_recipe` int(10) NOT NULL DEFAULT '0' COMMENT '药方能否显示 4购药前后可见药材名 3购药前后可见药材名和克数 2仅购药后可见药材名 1仅购药后可见药材名和克数 0均不可见',
  `hid` int(11) NOT NULL DEFAULT '0' COMMENT '医生帮手id',
  `shop_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '达达快递商铺编号',
  `dispatch_auto` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否自动派单,0,初始,1,自动待分配,4,自动失败,2,自动入队中,3,自动入队完成,5,客服转移到手工,6,特殊订单,7,自动完成',
  `prescript_price` double(16,2) NOT NULL DEFAULT '0.00' COMMENT '协定方价格',
  `rule_id` int(10) NOT NULL DEFAULT '0' COMMENT '自动派单规则id, 是否进入自动派单队列,0,未进入,>0,已进入',
  `status_recipel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ORDINARY' COMMENT '正常 ORDINARY,隐藏价格PRICE_HIDE,隐藏克数GRAM_HIDE, 隐藏价格和克数 PRICE_GRAM_HIDE, 隐藏价格和克数和药方总价 PRICE_GRAM_MONEY_HIDE, 只显示药方名CTM_TITLE, 不放处方HIDE，不显示电子签名ELECTRON_SIGN',
  `gcid` int(10) NOT NULL DEFAULT '0',
  `adjust_pid` int(10) NOT NULL DEFAULT '0' COMMENT '调整表id(价格调整)',
  `adjust_rid` int(10) NOT NULL DEFAULT '0' COMMENT '调整表id(奖励调整)',
  `dfirst` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否医生首单',
  `ufirst` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否用户首单',
  `status_recipel_doctor` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用药单隐藏处方 ORDINARY普通,CTM_TITLE 只显示中药费',
  `tp_daycount` int(11) NOT NULL DEFAULT '0' COMMENT '当天的派单顺序号',
  `pill_weight` int(10) NOT NULL DEFAULT '0' COMMENT '出丸重量(克)',
  `org_entity_id` int(11) NOT NULL DEFAULT '0' COMMENT '开方机构实体id',
  `is_trace_open` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持溯源开放 1：开放 2 ：关闭',
) ENGINE=InnoDB AUTO_INCREMENT=7217278 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='药方订单表';

医生表:
CREATE TABLE `boc_doctor` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一编号',
  `sort_id` int(11) DEFAULT '0' COMMENT '排序编号',
  `audit` int(1) NOT NULL DEFAULT '0' COMMENT '''0:未认证，1:已审核,  2:待审核，4:审核不通过 5. 二次待审核''',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '显示名称',
  `photo_share` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生邀请患者图片',
  `photo_guide_share` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '医生邀请指导海报',
  `wx_nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信昵称',
  `realname` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '手机',
  `unionid` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信用户识别码',
  `openid` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信openid',
  `copenid` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信小程序openid',
  `gcgy_openid` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '甘草国医openid',
  `gid` int(10) NOT NULL DEFAULT '2' COMMENT 'doctor_group 的 id',
  `referee_id` int(10) NOT NULL DEFAULT '0' COMMENT '邀请人ID',
  `referer_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '医生注册来源： 0：历史数据，1：自主注册，2：开方生成，3：医生邀请， 4：微信注册，7：甘草学园注册（弃用）， 71：甘草学园APP邀请， 72：甘草学园小程序邀请， 73：甘草学园PC自主注册， 74：甘草学园小程序自主注册',
  `brief` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '一句话介绍',
  `photo` int(10) NOT NULL DEFAULT '0' COMMENT '相关图片',
  `casteid` int(10) DEFAULT '1' COMMENT '职业等级',
  `reg_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `gender` int(10) NOT NULL DEFAULT '0',
  `is_index` int(10) NOT NULL DEFAULT '0' COMMENT '是否首页推荐',
  `is_student` int(10) NOT NULL DEFAULT '0' COMMENT '挂靠身份 是否为学生 默认为0，无证为1',
  `is_studio` int(10) NOT NULL DEFAULT '0' COMMENT '是否为工作室 默认0 ,1',
  `is_actived` int(10) NOT NULL DEFAULT '0' COMMENT '邀请激活',
  `pwd` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `pwd_errors` int(1) NOT NULL DEFAULT '0' COMMENT '密码错误次数',
  `chinaid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证',
  `is_face_real` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否实名认证',
  `face_real_photo` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实名认证人脸图片id',
  `email` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'email',
  `tel` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话',
  `pwd_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '密码状态\n0:需要修改\n1:正常\n2：弱密码\n3：强制修改密码',
  `phone_check` int(10) NOT NULL DEFAULT '0',
  `addr` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `xcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '推荐码',
  `status` int(1) NOT NULL DEFAULT '1' COMMENT '用户状态，0 禁用',
  `login_today` int(10) NOT NULL DEFAULT '0' COMMENT '今日登录次数',
  `login_ip` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后登录的IP',
  `login_ip_prev` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `reg_ip` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ip',
  `login_time` int(10) NOT NULL DEFAULT '0' COMMENT '最后一次登录时间',
  `login_time_prev` int(10) NOT NULL DEFAULT '0',
  `getpass` int(10) NOT NULL DEFAULT '0' COMMENT '获取密码时间',
  `photo_idcard_front` int(10) NOT NULL DEFAULT '0' COMMENT '身份证正面',
  `photo_idcard_front_origin` int(10) NOT NULL DEFAULT '0' COMMENT '身份证正面',
  `photo_idcard_back` int(11) NOT NULL DEFAULT '0' COMMENT '身份证反面',
  `photo_idcard_back_origin` int(11) NOT NULL DEFAULT '0' COMMENT '身份证反面',
  `photo_half` int(10) NOT NULL DEFAULT '0' COMMENT '手持半身照',
  `photo_occupation` int(10) NOT NULL DEFAULT '0' COMMENT '执业资格证=>医师资格证书',
  `photo_occupation2` int(10) NOT NULL DEFAULT '0' COMMENT '执业资格证2',
  `photo_reg` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `photo_reg2` int(10) NOT NULL DEFAULT '0' COMMENT '注册证书',
  `photo_cert` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '职称证书=>职称证书',
  `photo_badge` int(10) NOT NULL DEFAULT '0' COMMENT '工牌',
  `photo_site1` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '现场拜访照片',
  `photo_site` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '现场拜访照片',
  `photo_commission` int(10) NOT NULL DEFAULT '0' COMMENT '卫计委照片',
  `points` float NOT NULL DEFAULT '0',
  `money` float NOT NULL DEFAULT '0' COMMENT '医生诊金',
  `price_text` float NOT NULL DEFAULT '10' COMMENT '图文咨询',
  `price_text_vip` float NOT NULL DEFAULT '10' COMMENT '图文咨询复诊',
  `price_talk` float NOT NULL DEFAULT '10' COMMENT '电话咨询',
  `price_talk_vip` float NOT NULL DEFAULT '10' COMMENT '电话咨询复诊',
  `price_ask` float NOT NULL DEFAULT '5' COMMENT '提问服务',
  `price_video` float NOT NULL DEFAULT '20' COMMENT '视频咨询',
  `price_text_msg` int(10) NOT NULL DEFAULT '0' COMMENT '图文咨询消息条数',
  `price_talk_msg` int(10) NOT NULL DEFAULT '5' COMMENT '电话咨询消息条数',
  `price_follow_msg` int(11) NOT NULL DEFAULT '10' COMMENT '\n诊后随访消息条数\n',
  `price_scan_msg` int(10) NOT NULL DEFAULT '0' COMMENT '扫码咨询免费赠送条数',
  `price_ask_msg` int(10) unsigned NOT NULL DEFAULT '5' COMMENT '提问服务条数',
  `price_talk_time` int(11) NOT NULL DEFAULT '10' COMMENT '电话咨询时间',
  `price_ask_limit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '每日提问上限',
  `price_ask_time` int(10) unsigned NOT NULL DEFAULT '720' COMMENT '提问服务时长',
  `price_text_limit` int(11) NOT NULL DEFAULT '0' COMMENT '图文咨询上限',
  `price_talk_limit` int(11) NOT NULL DEFAULT '0' COMMENT '电话咨询上限',
  `price_video_limit` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '视频问诊上限',
  `price_video_vip` int(11) NOT NULL DEFAULT '20' COMMENT '视频问诊老患者价格',
  `disturb_start` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '23' COMMENT '免打扰开始时间',
  `disturb_end` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '7' COMMENT '免打扰结束时间',
  `disturb_buy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '免打扰时间是否能购买1:能',
  `auto_reply` tinyint(1) NOT NULL DEFAULT '0' COMMENT '自动回复是否开启1:开启',
  `auto_reply_word` varchar(550) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自动回复文案',
  `auto_reply_disturb_word` varchar(550) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '自动回复免打扰时间文案',
  `province` int(10) NOT NULL DEFAULT '0' COMMENT '执业省',
  `city` int(10) NOT NULL DEFAULT '0' COMMENT '执业市',
  `region` int(10) NOT NULL DEFAULT '0' COMMENT '执业区县',
  `hospital` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执业医院',
  `province_status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '执业地址状态,0,邀请医生填写的,1,医生本人填写的.',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门',
  `work_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工作电话',
  `price_text_on` int(10) NOT NULL DEFAULT '0' COMMENT '图文咨询开关',
  `price_talk_on` int(10) NOT NULL DEFAULT '0' COMMENT '语音咨询开关',
  `price_ask_on` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '提问服务开关',
  `followup_on` int(10) NOT NULL DEFAULT '1' COMMENT '诊后随访开关1：开启 0:关闭',
  `scan_on` int(10) NOT NULL DEFAULT '0' COMMENT '扫码赠送聊天开关 1：开启 0：关闭',
  `recipel_invest_on` int(10) NOT NULL DEFAULT '1' COMMENT '用药反馈单开关1:开启 0:关闭',
  `is_hide` tinyint(3) NOT NULL DEFAULT '0' COMMENT '0不隐身，1隐身',
  `price_video_on` int(10) NOT NULL DEFAULT '0' COMMENT '视频咨询开关',
  `value_star` float(8,2) DEFAULT '5.00' COMMENT '评分',
  `value_skill` float(8,2) DEFAULT '80.00' COMMENT '医疗水平',
  `value_service` float(8,2) DEFAULT '80.00' COMMENT '服务水平',
  `value_level` float(8,2) DEFAULT '80.00' COMMENT '整体评价(不渲染）',
  `judge_count` int(11) DEFAULT '0' COMMENT '用户对医生评论总数统计',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '擅长领域介绍',
  `intro_bg` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '医学教育背景',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '删除标志，0保留，1删除',
  `intro_achievement` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '医学成就学术成就',
  `is_agree` tinyint(1) DEFAULT '0',
  `notes` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `level` float(4,2) DEFAULT '1.00' COMMENT '甘草医生给医生的内部评级，默认为1',
  `level_gaofang` float(4,2) DEFAULT '1.00' COMMENT '甘草医生给医生的内部评级，默认为1',
  `photo_recipe_privilege` tinyint(1) DEFAULT '0' COMMENT '照片开方权限，0，没有，1，有',
  `up_region_id` int(11) unsigned DEFAULT '0' COMMENT '默认最上级的代理标签',
  `region_id` int(11) unsigned DEFAULT '0' COMMENT '医生的代理标签',
  `proxy_type` tinyint(1) DEFAULT '1' COMMENT '医生类型。默认为1，总代理为2，子代理为3',
  `timeupload` int(11) DEFAULT '0' COMMENT '上传资料时间',
  `timeupload_last` int(11) DEFAULT '0' COMMENT '最近上传资料时间',
  `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '医生公告',
  `randomly_limit` int(11) DEFAULT '0' COMMENT '每个月抢答上限，0为不限-1禁止抢答',
  `randomly_level` tinyint(3) NOT NULL DEFAULT '4' COMMENT '抢答等级,4优秀，3普通，2敷衍，1恶意,\n4:不限\n3:10秒\n2:60秒\n1:封号',
  `randomly_need_check` tinyint(4) DEFAULT '0' COMMENT '0不需要评审，1需要评审',
  `community_privilege` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'NONE' COMMENT 'NONE，无权限，ASK，提问权限，ANSWER,回答权限，FULL全部权限\r\n英[ˈprɪvəlɪdʒ]\r\n美[ˈprɪvəlɪdʒ, ˈprɪvlɪdʒ]\r\n',
  `community_money` double(16,2) DEFAULT '0.00' COMMENT '社区问诊费用',
  `timeaudit` int(11) DEFAULT '0' COMMENT '审核时间',
  `log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核日志',
  `has_ts` int(11) DEFAULT '0' COMMENT '是否有桌签  1有桌签',
  `contact_id` int(11) DEFAULT '0' COMMENT '医助ID manager-id',
  `contact_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '医助姓名[废弃]',
  `tags` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签集',
  `time_tags` int(11) NOT NULL DEFAULT '0' COMMENT '标签时间',
  `storage_group` int(11) NOT NULL DEFAULT '32' COMMENT '药库分组类型,0,根据地区自动匹配,其他指定为某个药库分组',
  `is_silent` int(11) NOT NULL DEFAULT '0' COMMENT '是否为有图文问诊的沉默用户',
  `wx_cash` tinyint(1) NOT NULL DEFAULT '1' COMMENT '微信提现 0未开通  1开通',
  `tax_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '缴税类型 \n1:劳务税(微信提现) \n2:劳务税(银行打款) \n3:第三方报税(银行打款) \n4:不交税(微信提现) \n5:公司贴税(银行打款) \n6:第三方报税(微信提现) \n10:自定义税率(银行打款)\n11:自定义税率(微信提现)',
  `tax_sign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '缴税签约(工猫) 0未签 1签约',
  `xbd_sign` tinyint(1) NOT NULL DEFAULT '0' COMMENT '山东晟和签约状态（薪八达）：0否，1是',
  `tax_rate` double(4,3) NOT NULL DEFAULT '0.000' COMMENT '缴税自定义额度',
  `photo_signature` int(11) NOT NULL DEFAULT '0' COMMENT '医生签名图片',
  `is_bankcard` int(1) NOT NULL DEFAULT '0' COMMENT '是否绑定银行卡 0:未绑定 1:已绑定',
  `pharmacy_seting` enum('','doctor_money_merge') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '医生用药单设置',
  `service_fee_recipe_rate` float(16,2) NOT NULL DEFAULT '0.00' COMMENT '医生普通方服务费 eg:0.1',
  `service_fee_cream_rate` float(16,2) NOT NULL DEFAULT '0.00' COMMENT '医生膏方服务费 eg:0.2',
  `is_succeed` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否继承 1：继承 2：非继承',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `is_change` tinyint(1) DEFAULT '0' COMMENT '0未迁移  1已经迁移',
  `is_entity_banner` tinyint(1) DEFAULT '0' COMMENT '是否接受实物锦旗，1为不接受，0为接受',
  `prescript_privilege` int(11) NOT NULL DEFAULT '0' COMMENT '0,常用方 ,1,协定方  2,其他',
  `proxy_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所属代理',
  `proxy_invite_level` int(11) unsigned DEFAULT '0' COMMENT '所属代理邀请的等级',
  `service_fee_recipe_lock` int(11) NOT NULL DEFAULT '0' COMMENT '普通方锁定服务费',
  `service_fee_cream_lock` int(11) NOT NULL DEFAULT '0' COMMENT '膏方锁定服务费',
  `service_fee_open_timeline` int(11) NOT NULL DEFAULT '0' COMMENT '医生服务费首次开通时间',
  `money_erase` tinyint(1) NOT NULL DEFAULT '0' COMMENT '我的收入隐藏',
  `recipe_erase` tinyint(1) NOT NULL DEFAULT '0' COMMENT '我的药方数量隐藏',
  `is_show_recipe` int(9) NOT NULL DEFAULT '0' COMMENT '药方能否显示 4购药前后可见药材名 3购药前后可见药材名和克数 2仅购药后可见药材名 1\n仅购药后可见药材名和克数 0均不可见',
  `switch_on` int(1) DEFAULT '0' COMMENT '临时开关 1：开启 0：关闭',
  `contract_privilege` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开通协定方,0不开通,1开通 ',
  `dispatch_auto` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否自动派单0 no 1 yes',
  `modify_bankcard` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可以修改银行卡姓名',
  `fi_tags` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '财务标签集',
  `fi_time_tags` int(11) NOT NULL DEFAULT '0' COMMENT '财务标签时间',
  `status_recipel` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PRICE_HIDE' COMMENT '正常 ORDINARY,隐藏价格PRICE_HIDE,隐藏克数GRAM_HIDE, 隐藏价格和克数 PRICE_GRAM_HIDE, 隐藏价格和克数和药方总价 PRICE_GRAM_MONEY_HIDE, 只显示药方名CTM_TITLE, 不放处方HIDE PRICE_MDOC_MREC_MPRO_MDEL_MDED,隐藏价格及Banner价格小计',
  `sea_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '公私海刷新标识',
  `is_decoction_default` tinyint(3) NOT NULL DEFAULT '1' COMMENT '二级剂型(饮片代煎偏好)',
  `registration_fee` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否收诊金,1,收,0不收',
  `receive_sms` tinyint(3) NOT NULL DEFAULT '1' COMMENT '1.正常.2,不收开方短信 ',
  `is_pack_fee` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否收包装材料费(膏方)',
  `photo_scan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邀请患者扫码推送医生卡片',
  `is_mall_share` tinyint(3) unsigned DEFAULT '1' COMMENT '是否显示商城商品推荐 0:隐藏 1:显示',
  `status_recipel_doctor---del` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ORDINARY' COMMENT '能否隐藏药方只显示标题',
  `dispatch_is_decoction` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '-2' COMMENT '半自动派单支持剂型',
  `status_recipel_switch` tinyint(2) NOT NULL DEFAULT '0' COMMENT '能否隐藏药方只显示标题',
  `is_auto_guide_chat` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否自动发送引导语',
  `guide_chat` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '欢迎找我在线复诊，请认真描述病情，稍后我会为你在线开方，付款后由专业药房抓药，药材最快当天可到。' COMMENT '引导语',
  `give_consultation_hours` int(10) unsigned NOT NULL DEFAULT '168' COMMENT '赠送咨询时长（小时）',
  `is_paying_subscribe` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否订单支付引导关注公众号',
  `is_urge_pay` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否催付，1无需，0催付',
) ENGINE=InnoDB AUTO_INCREMENT=273472 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='医生表';

地区表:
CREATE TABLE `boc_district` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `jc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简称',
  `parentid` int(11) NOT NULL COMMENT '父节点id',
  `initial` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第一个字简拼,如北京是b',
  `initials` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '汉字简拼，如北京是bj',
  `pinyin` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '汉字全拼,如北京是beijing',
  `extra` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `suffix` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行政区后缀(省、市、自治区、镇等)',
  `code` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '区域编码,街道没有独有的code，均继承父类（区县）的code',
  `area_code` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '城市编码',
  `order` tinyint(2) unsigned NOT NULL COMMENT '顺序',
  `area_level` enum('country','province','city','district','street','oversea') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行政区划级别.country:国家。province:省份（直辖市会在province和city显示）。city:市（直辖市会在province和city显示）。district:区县。street:街道.oversea:海外',
  `sys_time` datetime NOT NULL COMMENT '同步时间',
  `lng` decimal(9,6) NOT NULL DEFAULT '0.000000' COMMENT '经度: 高德',
  `lat` decimal(9,6) NOT NULL DEFAULT '0.000000' COMMENT '纬度:高德(海南台湾香港澳门及部分边远地区不准确)',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
) ENGINE=InnoDB AUTO_INCREMENT=1000538 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='行政区域表\n\nUPDATE  boc_configs_kv  SET   `comment` = ''20200819''  WHERE `id` = 118;';

-- boc_recipel.doctor_id can be joined with boc_doctor.id
-- boc_doctor 字段province city region 为省 市 区 id 与 boc_district.id 关联
-- 统计地区订单是按照医生表的省市区来进行统计


### Answer
给定数据库模式，下面是回答的 SQL 查询[QUESTION]{question}[/QUESTION]
[SQL]
"""

def query_generate_sql(question):
    # Flask应用的URL，根据你的设置可能需要修改
    url = "http://*********:5001/generate_sql"
    #url = "http://*********:5001/generate_sql"
    headers = {"Content-Type": "application/json"}
    data = {"question": question}

    response = requests.post(url, headers=headers, data=json.dumps(data))

    if response.status_code == 200:
        return response.json().get('generated_sql')
    else:
        return f"Error: {response.status_code}, Message: {response.text}"


question = "今天的天气"
updated_prompt = prompt.format(question=question)


generated_sql = query_generate_sql(updated_prompt)

print("输出sql:")
print(generated_sql)
