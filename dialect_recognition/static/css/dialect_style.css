/* 方言识别系统样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 30px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
    font-weight: 700;
}

h2, h3 {
    color: #3498db;
    margin-bottom: 15px;
}

.record-section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.status {
    margin: 0 15px;
    font-size: 16px;
    color: #7f8c8d;
    min-width: 100px;
    text-align: center;
}

.time {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    min-width: 60px;
    text-align: center;
}

.upload-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.file-input-wrapper {
    margin: 15px 0;
}

.file-input-wrapper input[type="file"] {
    display: none;
}

.file-label {
    display: inline-block;
    padding: 12px 24px;
    background-color: #3498db;
    color: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.file-label:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.results-section {
    margin-top: 30px;
}

.result-card {
    padding: 20px;
    background-color: #ecf0f1;
    border-radius: 8px;
    margin-bottom: 20px;
}

.result-text {
    min-height: 100px;
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.dialect-info {
    font-size: 16px;
    color: #2c3e50;
    font-weight: 600;
}

.dialect-info span {
    color: #e74c3c;
}

.audio-player {
    width: 100%;
    margin-top: 20px;
    border-radius: 50px;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    background-color: #fadbd8;
    color: #c0392b;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin: 20px 0;
    font-weight: 600;
}

.hidden {
    display: none;
} 