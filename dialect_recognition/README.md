# 讯飞方言语音识别系统

这是一个使用讯飞开放平台方言语音识别API的Web演示应用。该应用允许用户通过录音或上传音频文件，利用讯飞的方言大模型进行语音识别，支持普通话、简单英语和202种方言。

## 功能特点

- 实时录音并识别
- 上传音频文件进行识别
- 支持普通话和多种方言
- 方言类型自动识别
- 美观的用户界面

## 技术栈

- 前端：HTML、CSS、JavaScript
- 后端：Python Flask
- 服务API：讯飞开放平台方言识别API

## 快速开始

### 前提条件

- Python 3.6+
- Flask
- 讯飞开放平台账号（已配置）

### 安装

1. 克隆项目仓库
```
git clone [项目仓库URL]
cd dialect_recognition
```

2. 安装依赖
```
pip install -r requirements.txt
```

3. 运行应用
```
python app.py
```

4. 在浏览器中打开 `http://localhost:5000` 访问应用

## 使用说明

1. **录音识别**
   - 点击"开始录音"按钮开始录制
   - 点击"停止录音"按钮结束录制
   - 系统会自动处理音频并显示识别结果和方言类型

2. **上传音频文件**
   - 点击"选择音频文件"按钮选择要上传的音频
   - 点击"开始识别"按钮上传并识别
   - 系统会自动处理音频并显示识别结果和方言类型

## 注意事项

- 确保麦克风权限已授予浏览器
- 音频长度不应超过60秒（讯飞API限制）
- 支持的音频格式为PCM，采样率16kHz，位长16bit，单声道
- 应在HTTPS环境下运行，某些浏览器可能会限制非安全环境下的麦克风访问 