import requests
import json
import pandas as pd
import time
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openai import OpenAI

# 设置API相关参数
MODEL_API_BASE = "https://one-api.igancao.cn/v1"
CHAT_KEY = "sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc"
CHAT_MODEL = "qwen-vl-max-latest"

# 初始化OpenAI客户端
client = OpenAI(
    api_key=CHAT_KEY,
    base_url=MODEL_API_BASE
)

# 图片URL列表
image_urls = [
    "https://storage1.igancao.com/GC/M00/40/FA/9b74b180f17f3a76df18ff408e04ccf8.jpg",
    "https://storage1.igancao.com/GC/M00/E1/26/81d878ed2f10313bae7f55b9cfbbe9b1.jpg",
    "https://storage1.igancao.com/GC/M00/64/4A/6ecd457ed4a764fc0b049c8a96b8aee1.jpg",
    "https://storage1.igancao.com/GC/M00/64/5B/cb8a93dded96c2b635e3d356d743629d.jpg",
    "https://storage1.igancao.com/GC/M00/27/3E/6ba2acadb4df40ff7d9d904ba0609abb.jpg",
    "https://storage1.igancao.com/GC/M00/9/6A/6fca0c6c2c13c2d9177cf8813fe96482.jpg",
    "https://storage1.igancao.com/GC/M00/1F/B9/99d782f5857e1fcb7b08d0f4cb2242ff.jpg",
    "https://storage1.igancao.com/GC/M00/25/34/cae83f63db298279454974d0b572cb2e.jpg",
    "https://storage1.igancao.com/GC/M00/FB/B5/cbbef1b5de7ee9a4f221593592d719b9.jpg",
    "https://storage1.igancao.com/GC/M00/5C/6F/6d0679e34266fe1fdde1c3fed8b1e07d.jpg",
    "https://storage1.igancao.com/GC/M00/5E/42/abb58ff1945a742d7d21e0d710d69150.jpg",
    "https://storage1.igancao.com/GC/M00/82/B6/a522e9dd58a2d8bd913693261e110dd6.jpg",
    "https://storage1.igancao.com/GC/M00/B/FD/2e8869ec56e475c07e4b287267356cb2.jpg",
    "https://storage1.igancao.com/GC/M00/1F/BC/628c25aa54de7307336bf593da4ca5f5.jpg",
    "https://storage1.igancao.com/GC/M00/EF/18/d26ef6614f5f2261ec5a172e050dac70.jpg",
    "https://storage1.igancao.com/GC/M00/F7/D6/64b77da1824a059e5a75967dad4f8980.jpg",
    "https://storage1.igancao.com/GC/M00/51/60/6306c5ea4326e9ee0140d4ab05b6f538.jpg",
    "https://storage1.igancao.com/GC/M00/C4/95/0c85ed5077735d183b9f88231c10f0d5.jpg",
    "https://storage1.igancao.com/GC/M00/B6/45/67b809f4eb3d313395c34a6e9a1bdb07.jpg",
    "https://storage1.igancao.com/GC/M00/91/EF/69eff9de82082e8e7b03e9b9ee0ed8f8.jpg"
]

# 用多模态大模型分析舌象图片
def analyze_tongue_image(image_url):
    try:
        # 构建提示文本
        prompt = """分析这张舌象图片，请严格从以下每个特征的选项中选择最匹配的标签，按照以下格式输出，输出仅包含json格式:
        {
            "舌色": "从以下选项中选择一项：淡红舌、淡白舌、红舌、绛舌、青紫舌",
            "舌形": "从以下选项中选择一项或多项：老舌、嫩舌、胖舌、瘦舌、点刺舌、裂纹舌、齿痕舌、瘀斑舌",
            "苔色": "从以下选项中选择一项：白苔、黄苔、灰黑苔",
            "苔质": "从以下选项中选择一项或多项：薄苔、厚苔、润苔、燥苔、腻苔、腐苔、剥苔、假苔"
        }
         舌色选项说明：
        - "淡红舌"：舌色淡红润泽。
        - "淡白舌"：⽐正常⾆⾊浅淡。⾆⾊⽩⽽⼏⽆⾎⾊者，称为枯⽩⾆。
        - "红舌"：比正常舌色红，或呈鲜红色。
        - "绛舌"：较红舌颜色更深，或略带暗红色。
        - "青紫舌"：全舌淡紫或深紫色。

        舌形选项说明：
        - "老舌"：舌质纹里粗糙或皱缩，形色坚敛苍老，舌色较暗者。
        - "嫩舌"：舌质纹理细腻，形色浮胖娇嫩，舌色浅淡。
        - "胖舌"：舌体比正常舌大而厚，伸舌满口，或肿大满嘴。
        - "瘦舌"：舌体比正常舌瘦小而薄。
        - "点刺舌"：舌面有突起的红色、白色或黑色星点。
        - "裂纹舌"：舌面上出现各种形状的裂纹、裂沟。
        - "齿痕舌"：舌体边缘有牙齿压迫的痕迹。
        - "瘀斑舌"：舌上有紫暗色的斑点或斑块。

        苔色选项说明：
        - "白苔"：舌面上所附着的苔垢呈现白色（包括薄白苔和厚白苔）。
        - "黄苔"：舌苔呈现黄色（包括浅黄、深黄和焦黄）。
        - "灰黑苔"：舌苔呈灰色或黑色。

        苔质选项说明：
        - "薄苔"：透过舌苔能隐隐见到舌质。
        - "厚苔"：不能透过舌苔见到舌质。
        - "润苔"：舌苔润泽有津，干湿适中。
        - "燥苔"：舌苔干燥，望之干枯、扪之无津。
        - "腻苔"：苔质颗拉细腻致密，融合成片，如涂有油腻之状。
        - "腐苔"：苔质颗粒疏松，粗大而厚，形如豆腐渣堆积舌面。
        - "剥苔"：舌面本有舌苔，现舌苔全部或部分脱落。
        - "假苔"：舌苔不着实，似浮涂舌上，刮之即去。

        """

        # 调用大模型API
        response = client.chat.completions.create(
            model=CHAT_MODEL,
            messages=[
                {"role": "system", "content": "你是一个专业的中医舌象分析师，请根据图片分析舌象特征，并从给定的选项中为每个特征选择最匹配的标签。"},
                {"role": "user", "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": image_url}}
                ]}
            ],
            temperature=0.2,
            max_tokens=1000
        )
        
        # 提取回复内容
        content = response.choices[0].message.content
        
        # 从回复中提取JSON
        try:
            # 尝试找到JSON部分并解析
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                result = json.loads(json_str)
                # 添加图片URL
                result["图片URL"] = image_url
                return result
            else:
                # 如果找不到JSON格式，尝试按字段解析
                print(f"无法找到JSON格式，原始回复: {content}")
                return {"图片URL": image_url, "错误": "回复格式不正确", "原始回复": content}
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {str(e)}, 原始回复: {content}")
            return {"图片URL": image_url, "错误": "JSON解析错误", "原始回复": content}
            
    except Exception as e:
        print(f"API调用异常: {str(e)}")
        return {"图片URL": image_url, "错误": str(e)}

# 用于存储结果的列表
results = []

# 逐个处理每个URL
for i, url in enumerate(image_urls):
    print(f"正在处理第{i+1}/{len(image_urls)}个图片: {url}")
    
    # 分析图片
    result_dict = analyze_tongue_image(url)
    
    # 添加到结果列表
    results.append(result_dict)
    print(f"已完成: {url}")
    
    # 添加短暂延迟避免请求过快
    time.sleep(2)

# 将结果转换为DataFrame
df = pd.DataFrame(results)

# 调整列的顺序
columns = ["图片URL", "舌色", "舌形", "苔色", "苔质", "错误", "原始回复"]
df = df.reindex(columns=[col for col in columns if col in df.columns])

# 保存到Excel
excel_file = "多模态大模型舌象分析结果3.xlsx"
df.to_excel(excel_file, index=False)

print(f"所有结果已保存到 {excel_file}")

# 如果需要更漂亮的格式化Excel，可以使用openpyxl进一步处理
try:
    # 打开工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "多模态大模型舌象分析结果3"
    
    # 将数据写入工作表
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # 设置列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存美化后的Excel
    formatted_excel_file = "多模态大模型舌象分析结果_美化版3.xlsx"
    wb.save(formatted_excel_file)
    print(f"格式化的结果已保存到 {formatted_excel_file}")
except Exception as e:
    print(f"格式化Excel时出错: {str(e)}") 