import base64
import datetime
import hashlib
import hmac
import json
import os
import urllib.parse
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

# 讯飞开放平台配置（需要替换为自己的）
API_KEY = "b2b4c0868826f11dc3047298fad8e535"  # 在讯飞开放平台获取的APIKey
API_SECRET = "f1743ac61e6de11b642f73de53c201e8"  # 在讯飞开放平台获取的APISecret
APP_ID = "5940d9a5"  # 在讯飞开放平台获取的APPID
API_HOST = "iat.cn-huabei-1.xf-yun.com"  # 方言识别服务地址
API_PATH = "/v1"

# 配置上传文件存储路径
UPLOAD_FOLDER = 'static/uploads/audio'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    return render_template('dialect_recognition.html')

@app.route('/get_ws_url', methods=['GET'])
def get_ws_url():
    """生成WebSocket URL及认证信息"""
    try:
        # RFC1123格式的当前时间
        now = datetime.datetime.utcnow()
        date = now.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        # 生成认证签名
        signature_origin = f"host: {API_HOST}\ndate: {date}\nGET {API_PATH} HTTP/1.1"
        
        # 使用hmac-sha256算法结合apiSecret对signature_origin签名
        signature_sha = hmac.new(
            API_SECRET.encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        # 使用base64编码获得最终的signature
        signature = base64.b64encode(signature_sha).decode('utf-8')
        
        # 构建authorization_origin
        authorization_origin = f'api_key="{API_KEY}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature}"'
        
        # 对authorization_origin进行base64编码获得最终的authorization参数
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode('utf-8')
        
        # 构建WebSocket URL
        ws_url = f"wss://{API_HOST}{API_PATH}?authorization={urllib.parse.quote(authorization)}&date={urllib.parse.quote(date)}&host={API_HOST}"
        
        return jsonify({
            "ws_url": ws_url,
            "app_id": APP_ID
        })
    except Exception as e:
        return jsonify({"error": str(e)})

@app.route('/process_audio', methods=['POST'])
def process_audio():
    """处理上传的音频文件"""
    if 'audio' not in request.files:
        return jsonify({"error": "没有文件被上传"}), 400
    
    audio_file = request.files['audio']
    if audio_file.filename == '':
        return jsonify({"error": "没有选择文件"}), 400
    
    try:
        # 保存音频文件
        filename = f"audio_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.wav"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        audio_file.save(filepath)
        
        return jsonify({
            "success": True,
            "file_path": f"/static/uploads/audio/{filename}"
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True) 