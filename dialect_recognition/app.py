import base64
import datetime
import hashlib
import hmac
import json
import os
import urllib.parse
# import ssl  # 不需要SSL
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

# 讯飞开放平台配置
API_KEY = "b2b4c0868826f11dc3047298fad8e535"  # 在讯飞开放平台获取的APIKey
API_SECRET = "f1743ac61e6de11b642f73de53c201e8"  # 在讯飞开放平台获取的APISecret
APP_ID = "5940d9a5"  # 在讯飞开放平台获取的APPID
API_HOST = "iat.cn-huabei-1.xf-yun.com"  # 方言识别服务地址
API_PATH = "/v1"

# 配置上传文件存储路径
UPLOAD_FOLDER = 'static/uploads/audio'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/get_ws_url', methods=['GET'])
def get_ws_url():
    """生成WebSocket URL及认证信息"""
    try:
        # RFC1123格式的当前时间
        now = datetime.datetime.utcnow()
        date = now.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        # 生成认证签名
        signature_origin = f"host: {API_HOST}\ndate: {date}\nGET {API_PATH} HTTP/1.1"
        
        # 使用hmac-sha256算法结合apiSecret对signature_origin签名
        signature_sha = hmac.new(
            API_SECRET.encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        # 使用base64编码获得最终的signature
        signature = base64.b64encode(signature_sha).decode('utf-8')
        
        # 构建authorization_origin
        authorization_origin = f'api_key="{API_KEY}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature}"'
        
        # 对authorization_origin进行base64编码获得最终的authorization参数
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode('utf-8')
        
        # 构建WebSocket URL
        ws_url = f"wss://{API_HOST}{API_PATH}?authorization={urllib.parse.quote(authorization)}&date={urllib.parse.quote(date)}&host={API_HOST}"
        
        return jsonify({
            "ws_url": ws_url,
            "app_id": APP_ID
        })
    except Exception as e:
        return jsonify({"error": str(e)})

@app.route('/process_audio', methods=['POST'])
def process_audio():
    """处理上传的音频文件"""
    if 'audio' not in request.files:
        return jsonify({"error": "没有文件被上传"}), 400
    
    audio_file = request.files['audio']
    if audio_file.filename == '':
        return jsonify({"error": "没有选择文件"}), 400
    
    try:
        # 保存音频文件
        filename = f"audio_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.wav"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        audio_file.save(filepath)
        
        return jsonify({
            "success": True,
            "file_path": f"/static/uploads/audio/{filename}"
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    import socket
    # 获取本机IP地址
    # s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    # try:
    #     # 不需要真正连接
    #     s.connect(('10.0.0.0', 1))
    #     local_ip = s.getsockname()[0]
    # except Exception:
    #     local_ip = '127.0.0.1'
    # finally:
    #     s.close()
    #
    # print("\n方言识别系统已启动！")
    # print("=" * 50)
    # print(f"本地访问: http://127.0.0.1:5001")
    # print(f"局域网访问: http://{local_ip}:5001")
    # print("\n如果使用局域网IP访问时无法使用麦克风功能，请参考以下步骤配置浏览器：")
    # print("\nChrome/Edge浏览器：")
    # print("1. 在地址栏输入: chrome://flags/#unsafely-treat-insecure-origin-as-secure")
    # print(f"2. 在文本框中输入: http://{local_ip}:5001")
    # print("3. 将选项改为'Enabled'（启用）")
    # print("4. 重启浏览器")
    # print("\nFirefox浏览器：")
    # print("1. 在地址栏输入: about:config")
    # print("2. 搜索并启用: media.devices.insecure.enabled")
    # print("3. 搜索并启用: media.navigator.permission.disabled")
    # print("4. 重启浏览器")
    # print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5001)