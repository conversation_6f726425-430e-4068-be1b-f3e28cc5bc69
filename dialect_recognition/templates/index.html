<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方言语音识别系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dialect_style.css') }}">
    <style>
        /* 添加弹窗样式 */
        .permission-alert {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            text-align: center;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .permission-alert.active {
            display: block;
        }
        .permission-alert .close {
            float: right;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }
        
        /* 浏览器配置指南样式 */
        .guide-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #e2f4fd;
            border-radius: 5px;
            display: none;
        }
        .guide-section.show {
            display: block;
        }
        .guide-section h3 {
            color: #0056b3;
            margin-top: 0;
        }
        .guide-steps {
            text-align: left;
            margin-left: 20px;
        }
        .guide-steps li {
            margin-bottom: 10px;
        }
        .browser-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .browser-tab {
            padding: 8px 15px;
            background-color: #f0f0f0;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
        }
        .browser-tab.active {
            background-color: #0056b3;
            color: white;
        }
        .ip-address {
            font-weight: bold;
            color: #d63031;
        }
    </style>
</head>
<body>
    <!-- 添加麦克风权限提示 -->
    <div class="permission-alert" id="permissionAlert">
        <span class="close" onclick="closeAlert()">&times;</span>
        <p><strong>注意：</strong>如果显示"无法访问麦克风"，请按照以下步骤操作：</p>
        <p>1. 点击浏览器地址栏左侧的"不安全"图标</p>
        <p>2. 选择"网站设置"</p>
        <p>3. 在"麦克风"设置中选择"允许"</p>
        <p>4. 刷新页面</p>
        <p>如果仍然无法访问，<a href="#" id="showGuideLink">请点击此处查看详细配置指南</a></p>
    </div>

    <div class="container">
        <h1>方言语音识别系统</h1>
        
        <!-- 浏览器配置指南 -->
        <div class="guide-section" id="browserGuide">
            <h3>如何配置浏览器允许麦克风访问</h3>
            <div class="browser-tabs">
                <div class="browser-tab active" onclick="showBrowserGuide('chrome')">Chrome</div>
                <div class="browser-tab" onclick="showBrowserGuide('edge')">Edge</div>
                <div class="browser-tab" onclick="showBrowserGuide('firefox')">Firefox</div>
            </div>
            
            <div id="chrome-guide" class="browser-guide active">
                <ol class="guide-steps">
                    <li>在Chrome地址栏中输入：<code>chrome://flags/#unsafely-treat-insecure-origin-as-secure</code></li>
                    <li>在出现的搜索框中输入您的IP地址和端口：<span class="ip-address" id="currentIp">***********:5001</span></li>
                    <li>将选项从"Default"更改为"Enabled"（启用）</li>
                    <li>点击底部的"Relaunch"（重启）按钮重启浏览器</li>
                    <li>重启后，再次访问此页面并尝试录音</li>
                </ol>
            </div>
            
            <div id="edge-guide" class="browser-guide" style="display:none">
                <ol class="guide-steps">
                    <li>在Edge地址栏中输入：<code>edge://flags/#unsafely-treat-insecure-origin-as-secure</code></li>
                    <li>在出现的搜索框中输入您的IP地址和端口：<span class="ip-address" id="currentIpEdge">***********:5001</span></li>
                    <li>将选项从"Default"更改为"Enabled"（启用）</li>
                    <li>点击底部的"Restart"（重启）按钮重启浏览器</li>
                    <li>重启后，再次访问此页面并尝试录音</li>
                </ol>
            </div>
            
            <div id="firefox-guide" class="browser-guide" style="display:none">
                <ol class="guide-steps">
                    <li>在Firefox地址栏中输入：<code>about:config</code></li>
                    <li>在警告页面点击"接受风险并继续"</li>
                    <li>搜索 <code>media.devices.insecure.enabled</code> 并将其设置为 <code>true</code></li>
                    <li>搜索 <code>media.navigator.permission.disabled</code> 并将其设置为 <code>true</code></li>
                    <li>重启Firefox浏览器</li>
                    <li>重启后，再次访问此页面并尝试录音</li>
                </ol>
            </div>
        </div>
        
        <div class="record-section">
            <button id="startRecord" class="btn btn-primary">开始录音</button>
            <button id="stopRecord" class="btn btn-danger" disabled>停止录音</button>
            <div id="recordStatus" class="status">准备就绪</div>
            <div id="recordTime" class="time">00:00</div>
        </div>
        
        <div class="upload-section">
            <h3>或者上传音频文件</h3>
            <form id="uploadForm">
                <div class="file-input-wrapper">
                    <input type="file" id="audioFile" accept="audio/*" required>
                    <label for="audioFile" class="file-label">选择音频文件</label>
                </div>
                <button type="submit" class="btn btn-primary">开始识别</button>
            </form>
        </div>
        
        <div class="results-section">
            <h2>识别结果</h2>
            <div class="result-card">
                <div id="recognitionResult" class="result-text">等待识别...</div>
                <div id="dialectInfo" class="dialect-info">方言类型：<span>未知</span></div>
            </div>
            <audio id="audioPlayback" controls class="audio-player"></audio>
        </div>
        
        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>正在识别中...</p>
        </div>
        <div id="error" class="error hidden"></div>
    </div>
    <script src="{{ url_for('static', filename='js/dialect_recognition.js') }}"></script>
    <script>
        // 显示麦克风权限提示
        document.addEventListener('DOMContentLoaded', function() {
            // 填充当前IP地址
            const currentLocation = window.location.href;
            const ipAddressMatch = currentLocation.match(/\/\/([^\/]+)/);
            if (ipAddressMatch && ipAddressMatch[1]) {
                document.getElementById('currentIp').textContent = ipAddressMatch[1];
                document.getElementById('currentIpEdge').textContent = ipAddressMatch[1];
            }
            
            // 检查是否不是localhost
            if (!window.location.hostname.includes('127.0.0.1') && !window.location.hostname.includes('localhost')) {
                document.getElementById('permissionAlert').classList.add('active');
                
                // 15秒后自动关闭
                setTimeout(function() {
                    closeAlert();
                }, 15000);
            }
            
            // 监听麦克风错误
            document.getElementById('startRecord').addEventListener('click', function() {
                setTimeout(function() {
                    const errorElem = document.getElementById('error');
                    if (errorElem && !errorElem.classList.contains('hidden') && 
                        (errorElem.innerText.includes('麦克风') || errorElem.innerText.includes('访问'))) {
                        document.getElementById('permissionAlert').classList.add('active');
                    }
                }, 1000);
            });
            
            // 显示配置指南
            document.getElementById('showGuideLink').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('browserGuide').classList.add('show');
                // 滚动到指南部分
                document.getElementById('browserGuide').scrollIntoView({behavior: 'smooth'});
            });
        });
        
        function closeAlert() {
            document.getElementById('permissionAlert').classList.remove('active');
        }
        
        function showBrowserGuide(browser) {
            // 先隐藏所有指南
            const guides = document.querySelectorAll('.browser-guide');
            guides.forEach(guide => guide.style.display = 'none');
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.browser-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选定的指南
            document.getElementById(browser + '-guide').style.display = 'block';
            
            // 设置选定的标签为active
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html> 