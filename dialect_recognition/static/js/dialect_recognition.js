document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const startRecordBtn = document.getElementById('startRecord');
    const stopRecordBtn = document.getElementById('stopRecord');
    const recordStatus = document.getElementById('recordStatus');
    const recordTime = document.getElementById('recordTime');
    const uploadForm = document.getElementById('uploadForm');
    const audioFile = document.getElementById('audioFile');
    const recognitionResult = document.getElementById('recognitionResult');
    const dialectInfo = document.getElementById('dialectInfo').querySelector('span');
    const audioPlayback = document.getElementById('audioPlayback');
    const loading = document.getElementById('loading');
    const error = document.getElementById('error');

    // 录音相关变量
    let mediaRecorder;
    let audioChunks = [];
    let startTime;
    let timerInterval;
    let recordingBlob;

    // 初始化WebSocket连接参数
    let ws;
    let seq = 0;
    let appId = ""; // 存储从后端获取的app_id

    // 录音计时器
    function updateTimer() {
        const now = new Date();
        const diff = now - startTime;
        const mins = Math.floor(diff / 60000).toString().padStart(2, '0');
        const secs = Math.floor((diff % 60000) / 1000).toString().padStart(2, '0');
        recordTime.textContent = `${mins}:${secs}`;
    }

    // 开始录音
    startRecordBtn.addEventListener('click', async function() {
        try {
            audioChunks = [];
            
            // 检查浏览器是否支持mediaDevices API
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                // 兼容旧版浏览器
                const getUserMedia = navigator.getUserMedia ||
                                      navigator.webkitGetUserMedia ||
                                      navigator.mozGetUserMedia ||
                                      navigator.msGetUserMedia;
                
                if (!getUserMedia) {
                    throw new Error('您的浏览器不支持录音功能，请尝试升级浏览器或使用Chrome/Firefox');
                }
                
                // 使用旧版API
                getUserMedia({ audio: true }, 
                    function(stream) {
                        handleStream(stream);
                    },
                    function(error) {
                        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                            showError('麦克风访问被拒绝，请允许网站访问麦克风。如果使用的是局域网IP访问，请在Chrome地址栏输入：chrome://flags/#unsafely-treat-insecure-origin-as-secure，添加您的IP地址并启用此功能');
                        } else {
                            showError('无法访问麦克风: ' + error.message);
                        }
                        console.error('录音错误:', error);
                    }
                );
            } else {
                // 使用现代API
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    handleStream(stream);
                } catch (error) {
                    if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                        showError('麦克风访问被拒绝，请允许网站访问麦克风。如果使用的是局域网IP访问，Chrome用户请在地址栏输入：chrome://flags/#unsafely-treat-insecure-origin-as-secure，添加您的IP地址并启用此功能');
                    } else {
                        showError('无法访问麦克风: ' + error.message);
                    }
                    console.error('录音错误:', error);
                }
            }
        } catch (err) {
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                showError('麦克风访问被拒绝，请允许网站访问麦克风。如果使用的是局域网IP访问，Chrome用户请在地址栏输入：chrome://flags/#unsafely-treat-insecure-origin-as-secure，添加您的IP地址并启用此功能');
            } else {
                showError('无法访问麦克风: ' + err.message);
            }
            console.error('录音错误:', err);
        }
    });
    
    // 处理媒体流
    function handleStream(stream) {
        try {
            mediaRecorder = new MediaRecorder(stream);
            
            mediaRecorder.addEventListener('dataavailable', function(e) {
                audioChunks.push(e.data);
            });
            
            mediaRecorder.addEventListener('stop', function() {
                recordingBlob = new Blob(audioChunks, { type: 'audio/wav' });
                audioPlayback.src = URL.createObjectURL(recordingBlob);
                
                // 发送录音进行识别
                sendAudioForRecognition(recordingBlob);
            });
            
            // 更新UI
            startRecordBtn.disabled = true;
            stopRecordBtn.disabled = false;
            recordStatus.textContent = '正在录音...';
            recognitionResult.textContent = '等待识别...';
            dialectInfo.textContent = '未知';
            
            // 开始计时
            startTime = new Date();
            updateTimer();
            timerInterval = setInterval(updateTimer, 1000);
            
            // 开始录音
            mediaRecorder.start();
        } catch (err) {
            showError('录音初始化失败: ' + err.message);
            console.error('录音初始化错误:', err);
        }
    }

    // 停止录音
    stopRecordBtn.addEventListener('click', function() {
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
            mediaRecorder.stop();
            
            // 停止所有track
            mediaRecorder.stream.getTracks().forEach(track => track.stop());
            
            // 更新UI
            startRecordBtn.disabled = false;
            stopRecordBtn.disabled = true;
            recordStatus.textContent = '录音已停止';
            
            // 停止计时
            clearInterval(timerInterval);
        }
    });

    // 文件上传
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const file = audioFile.files[0];
        if (!file) {
            showError('请选择要上传的音频文件');
            return;
        }

        // 显示加载动画
        loading.classList.remove('hidden');
        error.classList.add('hidden');
        
        // 设置音频预览
        audioPlayback.src = URL.createObjectURL(file);
        
        // 发送音频文件进行识别
        sendAudioForRecognition(file);
    });

    // 发送音频进行识别
    async function sendAudioForRecognition(audioBlob) {
        try {
            // 显示加载状态
            loading.classList.remove('hidden');
            error.classList.add('hidden');
            recognitionResult.textContent = '正在识别中...';
            
            // 将音频转换为PCM格式
            const pcmData = await convertToPCM(audioBlob);
            
            // 初始化WebSocket连接
            initWebSocket(pcmData);
        } catch (err) {
            showError('音频处理失败: ' + err.message);
            console.error('音频处理错误:', err);
            loading.classList.add('hidden');
        }
    }

    // 初始化WebSocket连接
    function initWebSocket(audioData) {
        // 从后端获取WebSocket连接URL和认证信息
        fetch('/get_ws_url')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // 保存app_id
                appId = data.app_id;
                
                // 创建WebSocket连接
                ws = new WebSocket(data.ws_url);
                
                ws.onopen = function() {
                    console.log('WebSocket 连接已建立');
                    // 发送音频数据
                    sendAudioData(audioData);
                };
                
                ws.onmessage = function(event) {
                    handleResponse(event.data);
                };
                
                ws.onerror = function(event) {
                    console.error('WebSocket 错误:', event);
                    showError('WebSocket 连接错误');
                    loading.classList.add('hidden');
                };
                
                ws.onclose = function() {
                    console.log('WebSocket 连接已关闭');
                };
            })
            .catch(err => {
                showError('获取认证信息失败: ' + err.message);
                console.error('认证错误:', err);
                loading.classList.add('hidden');
            });
    }

    // 发送音频数据
    function sendAudioData(audioData) {
        // 分块发送音频数据
        const chunkSize = 1280; // 每个块大小
        let offset = 0;
        
        function sendNextChunk() {
            if (offset >= audioData.byteLength) {
                // 所有数据发送完毕，发送结束信号
                sendFrame(new Uint8Array(0), 2);
                return;
            }
            
            const end = Math.min(offset + chunkSize, audioData.byteLength);
            const chunk = audioData.slice(offset, end);
            
            // 发送音频帧
            const status = offset === 0 ? 0 : 1; // 0:开始，1:继续
            sendFrame(new Uint8Array(chunk), status);
            
            offset += chunkSize;
            
            // 设置间隔发送下一帧
            setTimeout(sendNextChunk, 40);
        }
        
        // 开始发送
        sendNextChunk();
    }

    // 发送单个音频帧
    function sendFrame(audioChunk, status) {
        if (ws.readyState !== WebSocket.OPEN) {
            return;
        }
        
        // 构建请求数据
        const data = {
            header: {
                app_id: appId, // 使用从后端获取的app_id
                status: status
            },
            parameter: {
                iat: {
                    language: "zh_cn",
                    accent: "mulacc", // 方言模式
                    domain: "slm",
                    eos: 1800,
                    dwa: "wpgs",
                    ptt: 1,
                    nunum: 1,
                    result: {
                        encoding: "utf8",
                        compress: "raw",
                        format: "json"
                    }
                }
            },
            payload: {
                audio: {
                    encoding: "raw",
                    sample_rate: 16000,
                    channels: 1,
                    bit_depth: 16,
                    status: status,
                    seq: seq++,
                    audio: arrayBufferToBase64(audioChunk)
                }
            }
        };
        
        console.log("发送音频帧，状态:", status);
        ws.send(JSON.stringify(data));
    }

    // 处理WebSocket响应
    function handleResponse(data) {
        try {
            const response = JSON.parse(data);
            console.log("收到WebSocket响应:", response);
            
            if (response.header.code !== 0) {
                throw new Error(`错误码: ${response.header.code}, 消息: ${response.header.message}`);
            }
            
            // 尝试从返回头部获取方言类型信息
            if (response.header && response.header.sid) {
                // sid格式通常为 "ase000e065f@dx193f81b97fb750c882"
                // 有时候可以从这个ID提取方言类型信息
                console.log("会话ID:", response.header.sid);
            }
            
            if (response.payload && response.payload.result && response.payload.result.text) {
                // 解码返回的text数据
                const resultText = response.payload.result.text;
                console.log("原始结果文本:", resultText);
                
                // 尝试base64解码
                try {
                    const decodedText = atob(resultText);
                    console.log("Base64解码后:", decodedText);
                    
                    try {
                        // 尝试解析JSON
                        const result = JSON.parse(decodedText);
                        console.log("解析后的JSON:", result);
                        
                        // 检查是否是替换或追加响应
                        const pgs = result.pgs || '';
                        const isReplace = pgs === 'rpl';
                        let currentText = recognitionResult.textContent;
                        
                        // 如果是空白或"等待识别..."或"正在识别中..."，重置当前文本
                        if (currentText === '等待识别...' || currentText === '正在识别中...' || currentText === '?') {
                            currentText = '';
                        }
                        
                        // 尝试提取方言类型信息
                        if (result.accent) {
                            updateDialectInfo(result.accent);
                        }
                        
                        // 更新识别结果
                        if (result.ws && result.ws.length > 0) {
                            let finalText = '';
                            let containsBrokenChars = false;
                            
                            // 检查是否有乱码字符
                            const allText = result.ws.map(item => 
                                item.cw && item.cw.length > 0 ? 
                                item.cw.map(word => word.w).join('') : 
                                '').join('');
                            
                            console.log("合并所有文本:", allText);
                            containsBrokenChars = /[\u00C0-\u00FF][\u0080-\u00BF]+/.test(allText);
                            
                            if (containsBrokenChars) {
                                // 尝试修复UTF-8编码的中文
                                try {
                                    // 先将所有文本合并以便解码
                                    const rawText = result.ws.map(item => 
                                        item.cw && item.cw.length > 0 ? 
                                        item.cw.map(word => word.w).join('') : 
                                        '').join('');
                                    
                                    // 尝试用TextDecoder解码
                                    // 这假设原始文本是UTF-8编码的，但在解析过程中出现了问题
                                    const fixedText = decodeURIComponent(escape(rawText));
                                    console.log("尝试修复后:", fixedText);
                                    
                                    finalText = fixedText;
                                } catch (encodeError) {
                                    console.error("尝试修复编码失败:", encodeError);
                                    
                                    // 如果修复失败，使用原始拼接文本，但是标记为可能有乱码
                                    let text = '';
                                    result.ws.forEach(item => {
                                        if (item.cw && item.cw.length > 0) {
                                            item.cw.forEach(word => {
                                                text += word.w;
                                            });
                                        }
                                    });
                                    finalText = text + " (可能包含乱码)";
                                }
                            } else {
                                // 正常拼接文本
                                result.ws.forEach(item => {
                                    if (item.cw && item.cw.length > 0) {
                                        item.cw.forEach(word => {
                                            finalText += word.w;
                                            
                                            // 检查是否包含方言类型信息
                                            if (word.accent) {
                                                updateDialectInfo(word.accent);
                                            }
                                        });
                                    }
                                });
                            }
                            
                            // 检查是否有文本，并处理追加/替换逻辑
                            if (finalText) {
                                if (isReplace && result.rg && Array.isArray(result.rg) && result.rg.length === 2) {
                                    // 替换模式: 服务器指示替换前一个响应的部分结果
                                    console.log("替换模式处理, 当前文本:", currentText, "要替换的部分索引:", result.rg);
                                    
                                    // 保留非替换部分的文本（如果有）
                                    // 由于我们只能跟踪整体文本而不是分段索引，这里简化处理
                                    recognitionResult.textContent = finalText;
                                } else {
                                    // 追加模式: 直接追加到当前结果
                                    console.log("追加模式处理, 当前文本:", currentText, "追加文本:", finalText);
                                    recognitionResult.textContent = currentText + finalText;
                                }
                            } else if (!currentText) {
                                recognitionResult.textContent = "未识别到文本内容";
                            }
                        } else {
                            if (!currentText) {
                                recognitionResult.textContent = "未识别到文本内容 (无ws字段)";
                            }
                        }
                        
                        // 通过内容推断可能的方言类型
                        if (response.header.status === 2 && recognitionResult.textContent) {
                            inferDialectFromContent(recognitionResult.textContent);
                        }
                        
                    } catch (jsonError) {
                        console.error("JSON解析失败:", jsonError);
                        if (recognitionResult.textContent === '等待识别...' || recognitionResult.textContent === '正在识别中...') {
                            recognitionResult.textContent = "解析识别结果失败";
                        }
                    }
                } catch (b64Error) {
                    console.error("Base64解码失败:", b64Error);
                    // 如果解码失败，直接显示原始文本，但不覆盖已有结果
                    if (recognitionResult.textContent === '等待识别...' || recognitionResult.textContent === '正在识别中...') {
                        recognitionResult.textContent = resultText;
                    }
                }
                
                // 如果是最后一个结果包，隐藏加载
                if (response.header.status === 2) {
                    loading.classList.add('hidden');
                    ws.close();
                }
            } else if (response.payload && response.payload.result) {
                // 没有text字段但有result字段
                console.log("收到无text字段的响应:", response);
                
                const result = response.payload.result;
                // 检查result是否有text字段
                if (result.text) {
                    // 获取当前文本，用于追加
                    let currentText = recognitionResult.textContent;
                    if (currentText === '等待识别...' || currentText === '正在识别中...') {
                        currentText = '';
                    }
                    
                    // 尝试解码base64
                    try {
                        const decodedText = atob(result.text);
                        console.log("从result.text解码后:", decodedText);
                        
                        try {
                            // 尝试解析JSON
                            const jsonResult = JSON.parse(decodedText);
                            console.log("从result.text解析的JSON:", jsonResult);
                            
                            // 尝试提取方言类型信息
                            if (jsonResult.accent) {
                                updateDialectInfo(jsonResult.accent);
                            }
                            
                            // 检查是否是替换或追加响应
                            const pgs = jsonResult.pgs || '';
                            const isReplace = pgs === 'rpl';
                            
                            // 提取文本内容
                            if (jsonResult.ws && jsonResult.ws.length > 0) {
                                let finalText = '';
                                let containsBrokenChars = false;
                                
                                // 检查是否有乱码字符
                                const allText = jsonResult.ws.map(item => 
                                    item.cw && item.cw.length > 0 ? 
                                    item.cw.map(word => word.w).join('') : 
                                    '').join('');
                                
                                console.log("合并所有文本:", allText);
                                containsBrokenChars = /[\u00C0-\u00FF][\u0080-\u00BF]+/.test(allText);
                                
                                if (containsBrokenChars) {
                                    // 尝试修复UTF-8编码的中文
                                    try {
                                        // 先将所有文本合并以便解码
                                        const rawText = jsonResult.ws.map(item => 
                                            item.cw && item.cw.length > 0 ? 
                                            item.cw.map(word => word.w).join('') : 
                                            '').join('');
                                        
                                        // 尝试用TextDecoder解码
                                        // 这假设原始文本是UTF-8编码的，但在解析过程中出现了问题
                                        const fixedText = decodeURIComponent(escape(rawText));
                                        console.log("尝试修复后:", fixedText);
                                        
                                        finalText = fixedText;
                                    } catch (encodeError) {
                                        console.error("尝试修复编码失败:", encodeError);
                                        
                                        // 如果修复失败，使用原始拼接文本，但是标记为可能有乱码
                                        let text = '';
                                        jsonResult.ws.forEach(item => {
                                            if (item.cw && item.cw.length > 0) {
                                                item.cw.forEach(word => {
                                                    text += word.w;
                                                    
                                                    // 检查是否包含方言类型信息
                                                    if (word.accent) {
                                                        updateDialectInfo(word.accent);
                                                    }
                                                });
                                            }
                                        });
                                        finalText = text + " (可能包含乱码)";
                                    }
                                } else {
                                    // 正常拼接文本
                                    jsonResult.ws.forEach(item => {
                                        if (item.cw && item.cw.length > 0) {
                                            item.cw.forEach(word => {
                                                finalText += word.w;
                                                
                                                // 检查是否包含方言类型信息
                                                if (word.accent) {
                                                    updateDialectInfo(word.accent);
                                                }
                                            });
                                        }
                                    });
                                }
                                
                                // 检查是否有文本，并处理追加/替换逻辑
                                if (finalText) {
                                    if (isReplace && jsonResult.rg && Array.isArray(jsonResult.rg) && jsonResult.rg.length === 2) {
                                        // 替换模式: 服务器指示替换前一个响应的部分结果
                                        console.log("result.text 替换模式处理, 当前文本:", currentText);
                                        recognitionResult.textContent = finalText;
                                    } else {
                                        // 追加模式: 直接追加到当前结果
                                        console.log("result.text 追加模式处理, 当前文本:", currentText, "追加文本:", finalText);
                                        recognitionResult.textContent = currentText + finalText;
                                    }
                                } else if (!currentText) {
                                    recognitionResult.textContent = "未识别到文本内容";
                                }
                            } else {
                                if (!currentText) {
                                    recognitionResult.textContent = "未识别到文本内容";
                                }
                            }
                            
                            // 通过内容推断可能的方言类型
                            if (response.header.status === 2 && recognitionResult.textContent) {
                                inferDialectFromContent(recognitionResult.textContent);
                            }
                            
                        } catch (jsonError) {
                            console.error("从result.text解析JSON失败:", jsonError);
                            if (recognitionResult.textContent === '等待识别...' || recognitionResult.textContent === '正在识别中...') {
                                recognitionResult.textContent = decodedText;
                            }
                        }
                    } catch (b64Error) {
                        console.error("解码result.text失败:", b64Error);
                        if (recognitionResult.textContent === '等待识别...' || recognitionResult.textContent === '正在识别中...') {
                            recognitionResult.textContent = result.text;
                        }
                    }
                } else {
                    if (recognitionResult.textContent === '等待识别...' || recognitionResult.textContent === '正在识别中...') {
                        recognitionResult.textContent = "接收到服务器响应，但无法解析文本内容";
                    }
                }
            }
        } catch (err) {
            showError('解析结果失败: ' + err.message);
            console.error('响应处理错误:', err);
            loading.classList.add('hidden');
            if (ws) ws.close();
        }
    }

    // 将音频转换为PCM格式
    async function convertToPCM(audioBlob) {
        return new Promise((resolve, reject) => {
            // 这里我们简化处理，实际应用中可能需要更复杂的音频转换
            // 使用AudioContext进行处理
            const fileReader = new FileReader();
            
            fileReader.onload = function(event) {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                audioContext.decodeAudioData(event.target.result)
                    .then(audioBuffer => {
                        // 获取PCM数据
                        const pcmData = audioBufferToPCM(audioBuffer);
                        resolve(pcmData);
                    })
                    .catch(err => {
                        reject(new Error('音频解码失败: ' + err.message));
                    });
            };
            
            fileReader.onerror = function() {
                reject(new Error('文件读取失败'));
            };
            
            fileReader.readAsArrayBuffer(audioBlob);
        });
    }

    // 将AudioBuffer转换为PCM格式
    function audioBufferToPCM(audioBuffer) {
        const numChannels = 1; // 单声道
        const length = audioBuffer.length;
        const sampleRate = audioBuffer.sampleRate; // 获取原始采样率
        
        // 如果需要重采样到16kHz
        let resampledData;
        if (sampleRate !== 16000) {
            console.log(`需要从 ${sampleRate}Hz 重采样到 16000Hz`);
            resampledData = resampleAudio(audioBuffer, 16000);
        } else {
            resampledData = audioBuffer.getChannelData(0);
        }
        
        // 创建16bit PCM缓冲区
        const buffer = new ArrayBuffer(resampledData.length * 2);
        const view = new DataView(buffer);
        
        // 将Float32采样数据转换为Int16
        for (let i = 0; i < resampledData.length; i++) {
            const sample = Math.max(-1, Math.min(1, resampledData[i]));
            view.setInt16(i * 2, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        }
        
        return buffer;
    }
    
    // 简单的重采样函数
    function resampleAudio(audioBuffer, targetSampleRate) {
        const originalSampleRate = audioBuffer.sampleRate;
        const originalData = audioBuffer.getChannelData(0);
        
        if (originalSampleRate === targetSampleRate) {
            return originalData;
        }
        
        const ratio = originalSampleRate / targetSampleRate;
        const newLength = Math.round(originalData.length / ratio);
        const result = new Float32Array(newLength);
        
        for (let i = 0; i < newLength; i++) {
            const pos = Math.floor(i * ratio);
            result[i] = originalData[pos];
        }
        
        return result;
    }

    // 将ArrayBuffer转换为Base64
    function arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    // 显示错误信息
    function showError(message) {
        error.textContent = message;
        error.classList.remove('hidden');
        loading.classList.add('hidden');
    }

    // 添加一个测试按钮，模拟有内容的识别结果
    const testBtn = document.createElement('button');
    testBtn.textContent = '测试模拟识别';
    testBtn.className = 'btn btn-primary';
    testBtn.style.marginTop = '20px';
    testBtn.addEventListener('click', function() {
        const mockResultJSON = {
            "ws": [
                {
                    "bg": 0,
                    "cw": [
                        {
                            "sc": 0.0,
                            "w": "你好"
                        }
                    ]
                },
                {
                    "bg": 0,
                    "cw": [
                        {
                            "sc": 0.0,
                            "w": "世界"
                        }
                    ]
                }
            ],
            "sn": 1,
            "ls": true,
            "bg": 0,
            "ed": 0,
            "pgs": "apd",
            "rst": "rlt"
        };
        
        // 显示模拟结果
        let text = '';
        mockResultJSON.ws.forEach(item => {
            if (item.cw && item.cw.length > 0) {
                item.cw.forEach(word => {
                    text += word.w;
                });
            }
        });
        
        recognitionResult.textContent = text;
        dialectInfo.textContent = "测试模拟";
    });
    
    document.querySelector('.container').appendChild(testBtn);

    // 更新方言信息显示
    function updateDialectInfo(accentInfo) {
        console.log("接收到方言信息:", accentInfo);
        
        if (!accentInfo) return;
        
        // 方言代码映射表
        const dialectMap = {
            'mandarin': '普通话',
            'cantonese': '粤语',
            'lmz': '闽南语',
            'henanese': '河南话',
            'sichuanese': '四川话',
            'shanghainese': '上海话',
            'hakka': '客家话',
            'jilu': '冀鲁官话',
            'fujian': '福建话',
            'hubei': '湖北话',
            'hunan': '湖南话',
            'en': '英语',
            'mulacc': '多语种'
        };
        
        // 更新方言类型显示
        if (dialectMap[accentInfo]) {
            dialectInfo.textContent = dialectMap[accentInfo];
        } else {
            dialectInfo.textContent = accentInfo;
        }
    }
    
    // 通过内容推断可能的方言类型
    function inferDialectFromContent(content) {
        if (!content || dialectInfo.textContent !== '未知') return;
        
        console.log("尝试从内容推断方言类型:", content);
        
        // 一些简单的方言特征检测规则
        const dialectPatterns = [
            { pattern: /[嘅嗰乜嘢噉咁冇佢]/g, dialect: '粤语' },
            { pattern: /[厶恁咧]\s*[啥米]/g, dialect: '闽南语' },
            { pattern: /[晓得造么]/g, dialect: '上海话' },
            { pattern: /[嫑莫晓得]/g, dialect: '四川话' },
            { pattern: /乃/.test(content) && /们/.test(content) === false, dialect: '河南话' },
            { pattern: /hello|hi|how|what|when|where/i, dialect: '英语' }
        ];
        
        for (const { pattern, dialect } of dialectPatterns) {
            if (pattern.test(content)) {
                dialectInfo.textContent = dialect;
                console.log("通过内容推断为:", dialect);
                return;
            }
        }
        
        // 如果长度超过5个字符但没有特殊方言特征，很可能是普通话
        if (content.length > 5 && /^[\u4e00-\u9fa5]+$/.test(content)) {
            dialectInfo.textContent = '普通话 (推断)';
            console.log("通过内容推断可能是普通话");
        }
    }
}); 