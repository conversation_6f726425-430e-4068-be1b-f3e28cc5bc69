import pandas as pd
import requests
from io import BytesIO
from PIL import Image
import os
from openpyxl import load_workbook
from openpyxl.drawing.image import Image as XLImage

# 读取已有的Excel文件
excel_file = "多模态大模型舌象分析结果.xlsx"  # 替换为你的Excel文件名
output_file = "带图片的舌象分析结果.xlsx"  # 输出的Excel文件名

# 创建临时图片文件夹
temp_dir = "temp_images"
if not os.path.exists(temp_dir):
    os.makedirs(temp_dir)

# 复制原始Excel文件
df = pd.read_excel(excel_file)
df.to_excel(output_file, index=False)

# 加载工作簿以添加图片
wb = load_workbook(output_file)
ws = wb.active

# 设置图片的目标尺寸
img_width = 100  # 图片宽度（像素）
img_height = 75  # 图片高度（像素）

# 设置第B列的宽度以适应图片
ws.column_dimensions['B'].width = 15  # 调整B列宽度

# 设置行高以适应图片
for row in range(2, len(df) + 2):  # 从第2行开始（跳过标题行）
    ws.row_dimensions[row].height = 60  # 设置行高

# 遍历每一行的URL并添加图片
print(f"开始处理共{len(df)}个图片...")
for idx, row in df.iterrows():
    # 获取URL（第一列）
    url = row["图片URL"]
    row_idx = idx + 2  # Excel行索引（加2是因为Excel从1开始，还有标题行）
    
    try:
        print(f"处理第{idx+1}/{len(df)}个图片: {url}")
        
        # 下载图片
        response = requests.get(url)
        if response.status_code == 200:
            # 保存临时图片文件
            temp_img_path = os.path.join(temp_dir, f"img_{idx}.jpg")
            with open(temp_img_path, "wb") as f:
                f.write(response.content)
            
            # 使用PIL调整图片大小
            img = Image.open(temp_img_path)
            img = img.resize((img_width, img_height))
            img.save(temp_img_path)
            
            # 将图片添加到Excel中
            excel_img = XLImage(temp_img_path)
            # 设置图片位置到B列
            ws.add_image(excel_img, f'B{row_idx}')
            
            print(f"已添加图片: {url}")
        else:
            print(f"下载图片失败，状态码: {response.status_code}, URL: {url}")
    except Exception as e:
        print(f"处理图片时出错: {str(e)}, URL: {url}")

# 保存工作簿
wb.save(output_file)
print(f"所有图片已添加到Excel文件: {output_file}")

# 清理临时文件
for file in os.listdir(temp_dir):
    os.remove(os.path.join(temp_dir, file))
os.rmdir(temp_dir)
print("临时文件已清理") 