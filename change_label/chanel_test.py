def replace_labels(input_file, output_file):
    old_labels = [
        'Title', 'Subtitle', 'Text', 'Figure', 'Figure descripition',
        'Table', 'Table descripition', 'Reference', 'Formula',
        'Footer', 'Header', 'Choice quesiton', 'Option', 'Page number',
        'Code', 'Author', 'Organ'
    ]

    new_labels = [
        'Title', 'Author', 'Organ', 'Email address', 'Text', 'Footer',
        'Reference', 'Formula', 'Figure', 'Figure caption', 'Table',
        'Table descripition', 'Code', 'Code descripition', 'Pseduo code',
        'Header', 'Date', 'Link', 'Nation', 'Correspondence',
        'Choice question', 'Option', 'Subtitle','Figure descripition', 'Choice quesiton','Page number'
    ]

    label_mapping = dict(zip(old_labels, new_labels))

    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            parts = line.strip().split()
            if len(parts) > 1:
                label = old_labels[int(parts[0])]
                index = new_labels.index(label)
                updated_line = f"{index} {' '.join(parts[1:])}\n"
                outfile.write(updated_line)
            else:
                outfile.write(line)

if __name__ == "__main__":
    input_file = "./labels/test/10030.txt"  # 替换为YOLO标注文件的路径
    output_file = "test.txt"  # 替换为输出文件的路径

    replace_labels(input_file, output_file)
    print("标签已按照顺序替换完成。")